{"message":"Loading 129 CA(s) from :otp store","timestamp":*************,"level":"info","payload":{"function":"handle_shared_state_initialization/2","line":269,"module":"tls_certificate_check_shared_state","time":1752088103525591,"file":"/workspace/crosspost/deps/tls_certificate_check/src/tls_certificate_check_shared_state.erl","mfa":"{:tls_certificate_check_shared_state, :handle_shared_state_initialization, 2}"}}
{"message":"Migrations already up","timestamp":*************,"level":"info","payload":{"function":"log/2","line":811,"module":"Elixir.Ecto.Migrator","time":****************,"file":"lib/ecto/migrator.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Migrator, :log, 2}"}}
{"message":"Service auth failed","timestamp":*************,"level":"error","payload":{"error":"Invalid token","function":"get_service_auth/2","line":472,"module":"Elixir.Crosspost.Accounts.Bsky.Client","status":400,"time":****************,"file":"lib/crosspost/accounts/bsky/client.ex","domain":["elixir"],"application":"crosspost","mfa":"{Crosspost.Accounts.Bsky.Client, :get_service_auth, 2}","event":"bsky.client.get_service_auth.error"}}
{"message":"Failed to get service auth for video upload","timestamp":*************,"level":"error","payload":{"function":"upload_video/4","line":252,"module":"Elixir.Crosspost.Accounts.Bsky.Client","reason":"\"Bluesky API error: 400 - %{\\\"error\\\" => \\\"Invalid token\\\"}\"","time":****************,"file":"lib/crosspost/accounts/bsky/client.ex","domain":["elixir"],"application":"crosspost","mfa":"{Crosspost.Accounts.Bsky.Client, :upload_video, 4}","event":"bsky.client.upload_video.service_auth_failed"}}
{"message":"Video status check failed","timestamp":*************,"level":"error","payload":{"error":"Invalid job ID","function":"get_video_status/2","line":291,"module":"Elixir.Crosspost.Accounts.Bsky.Client","status":400,"time":****************,"file":"lib/crosspost/accounts/bsky/client.ex","domain":["elixir"],"application":"crosspost","mfa":"{Crosspost.Accounts.Bsky.Client, :get_video_status, 2}","event":"bsky.client.get_video_status.error"}}
{"message":"Failed to create post","timestamp":*************,"level":"error","payload":{"error":"Invalid request","function":"create_post/6","line":109,"module":"Elixir.Crosspost.Accounts.Bsky.Client","status":400,"time":****************,"file":"lib/crosspost/accounts/bsky/client.ex","domain":["elixir"],"application":"crosspost","mfa":"{Crosspost.Accounts.Bsky.Client, :create_post, 6}","event":"bsky.client.create_post.error"}}
{"message":"Session refresh failed","timestamp":*************,"level":"error","payload":{"function":"refresh_session/1","line":342,"module":"Elixir.Crosspost.Accounts.Bsky.Client","status":401,"time":****************,"file":"lib/crosspost/accounts/bsky/client.ex","domain":["elixir"],"application":"crosspost","mfa":"{Crosspost.Accounts.Bsky.Client, :refresh_session, 1}","event":"bsky.client.refresh_session.error"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":1.4,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":6.1,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"email\",\"confirmed_at\",\"customer\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", \"<EMAIL>\", ~U[2025-07-09 19:08:24Z], false, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.0,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":2.0,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303400319\", \"test-workspace--576460752303400319-ccaada\", %{}, true, 15932, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104118329,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.9,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18695, ~N[2025-07-09 19:08:24], 15932]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104124062,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18695]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104125420,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":1.0,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15932, %{}, 18695, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104134419,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":5.2,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15932]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104134823,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303400063\", \"test-workspace--576460752303400063\", %{}, false, 15932, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104138914,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.1,"query":"UPDATE \"workspaces\" SET \"invitations\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [[%{\"code\" => \"Su5qOkwNL498Jck2Pxf-cw\", \"email\" => \"<EMAIL>\", \"invited_at\" => \"2025-07-09T19:08:24Z\", \"valid_until\" => \"2025-07-16T19:08:24Z\"}], ~N[2025-07-09 19:08:24], 18696]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104139397,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.7,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"email\",\"confirmed_at\",\"customer\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", \"<EMAIL>\", ~U[2025-07-09 19:08:24Z], false, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.0,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.3,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303399935\", \"test-workspace--576460752303399935-dnqraq\", %{}, true, 15933, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104141630,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.2,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18697, ~N[2025-07-09 19:08:24], 15933]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104141909,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18697]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104142302,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15933, %{}, 18697, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104143889,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":1.5,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15933]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104144213,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303399807\", \"test-workspace--576460752303399807\", %{}, false, 15933, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104144530,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.1,"query":"UPDATE \"workspaces\" SET \"invitations\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [[%{\"code\" => \"jz64tNlkdhp3gWBFiuLaOw\", \"email\" => \"<EMAIL>\", \"invited_at\" => \"2025-07-09T19:08:24Z\", \"valid_until\" => \"2025-07-16T19:08:24Z\"}], ~N[2025-07-09 19:08:24], 18698]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104144915,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.0,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.6,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"email\",\"confirmed_at\",\"customer\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", \"<EMAIL>\", ~U[2025-07-09 19:08:24Z], false, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.0,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303399679\", \"test-workspace--576460752303399679-haqvjg\", %{}, true, 15934, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104147042,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.2,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18699, ~N[2025-07-09 19:08:24], 15934]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104147370,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.2,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18699]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104147804,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15934, %{}, 18699, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104149363,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":1.4,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15934]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104149593,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303399551\", \"test-workspace--576460752303399551\", %{}, false, 15934, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104149916,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.1,"query":"UPDATE \"workspaces\" SET \"invitations\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [[%{\"code\" => \"G1pLkj0_qYWAi29r1r8aSw\", \"email\" => \"<EMAIL>\", \"invited_at\" => \"2025-07-09T19:08:24Z\", \"valid_until\" => \"2025-07-16T19:08:24Z\"}], ~N[2025-07-09 19:08:24], 18700]","source":"workspaces","status":"OK"}}
{"message":"Finding invitation","timestamp":*************,"level":"debug","payload":{"code":"G1pLkj0_qYWAi29r1r8aSw","function":"find_invitation/2","line":108,"module":"Elixir.Crosspost.Accounts.Workspace","time":****************,"file":"lib/crosspost/accounts/workspace.ex","domain":["elixir"],"application":"crosspost","mfa":"{Crosspost.Accounts.Workspace, :find_invitation, 2}","workspace_id":18700,"invitations":[{"code":"G1pLkj0_qYWAi29r1r8aSw","email":"<EMAIL>","invited_at":"2025-07-09T19:08:24Z","valid_until":"2025-07-16T19:08:24Z"}]}}
{"message":"Checking invitation validity","timestamp":*************,"level":"debug","payload":{"code":"G1pLkj0_qYWAi29r1r8aSw","function":"find_invitation/2","line":123,"module":"Elixir.Crosspost.Accounts.Workspace","now":{"microsecond":"{149936, 6}","second":24,"calendar":"Elixir.Calendar.ISO","month":7,"day":9,"year":2025,"minute":8,"hour":19,"time_zone":"Etc/UTC","zone_abbr":"UTC","utc_offset":0,"std_offset":0},"time":****************,"file":"lib/crosspost/accounts/workspace.ex","domain":["elixir"],"application":"crosspost","mfa":"{Crosspost.Accounts.Workspace, :find_invitation, 2}","valid_until":{"microsecond":"{0, 0}","second":24,"calendar":"Elixir.Calendar.ISO","month":7,"day":16,"year":2025,"minute":8,"hour":19,"time_zone":"Etc/UTC","zone_abbr":"UTC","utc_offset":0,"std_offset":0},"comparison":"gt"}}
{"message":"Invitation validity check","timestamp":*************,"level":"debug","payload":{"code":"G1pLkj0_qYWAi29r1r8aSw","function":"find_invitation/2","line":141,"module":"Elixir.Crosspost.Accounts.Workspace","time":****************,"file":"lib/crosspost/accounts/workspace.ex","domain":["elixir"],"application":"crosspost","mfa":"{Crosspost.Accounts.Workspace, :find_invitation, 2}","valid":true,"invitation_code":"G1pLkj0_qYWAi29r1r8aSw"}}
{"message":"Find invitation result","timestamp":*************,"level":"debug","payload":{"code":"G1pLkj0_qYWAi29r1r8aSw","function":"find_invitation/2","line":150,"module":"Elixir.Crosspost.Accounts.Workspace","time":****************,"file":"lib/crosspost/accounts/workspace.ex","domain":["elixir"],"application":"crosspost","mfa":"{Crosspost.Accounts.Workspace, :find_invitation, 2}","found":true}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.0,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.6,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"email\",\"confirmed_at\",\"customer\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", \"<EMAIL>\", ~U[2025-07-09 19:08:24Z], false, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.0,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303399359\", \"test-workspace--576460752303399359-g5rq3g\", %{}, true, 15935, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104152359,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.2,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18701, ~N[2025-07-09 19:08:24], 15935]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104152617,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.2,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18701]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104153060,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15935, %{}, 18701, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104154782,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":1.6,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15935]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104155232,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.5,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"email\",\"confirmed_at\",\"customer\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", \"<EMAIL>\", ~U[2025-07-09 19:08:24Z], false, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.4,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303423358\", \"test-workspace--576460752303423358-drmyda\", %{}, true, 15936, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104157574,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.2,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18702, ~N[2025-07-09 19:08:24], 15936]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104158040,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.3,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18702]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104158641,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15936, %{}, 18702, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104160484,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":1.7,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15936]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104160821,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303423484\", \"test-workspace--576460752303423484\", %{}, false, 15936, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"message":"Finding invitation","timestamp":*************,"level":"debug","payload":{"code":"invalid_code","function":"find_invitation/2","line":108,"module":"Elixir.Crosspost.Accounts.Workspace","time":****************,"file":"lib/crosspost/accounts/workspace.ex","domain":["elixir"],"application":"crosspost","mfa":"{Crosspost.Accounts.Workspace, :find_invitation, 2}","workspace_id":18703}}
{"message":"Find invitation result","timestamp":*************,"level":"debug","payload":{"code":"invalid_code","function":"find_invitation/2","line":150,"module":"Elixir.Crosspost.Accounts.Workspace","time":****************,"file":"lib/crosspost/accounts/workspace.ex","domain":["elixir"],"application":"crosspost","mfa":"{Crosspost.Accounts.Workspace, :find_invitation, 2}","found":false}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.7,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"email\",\"confirmed_at\",\"customer\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", \"<EMAIL>\", ~U[2025-07-09 19:08:24Z], false, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.3,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303399167\", \"test-workspace--576460752303399167-wkwxjq\", %{}, true, 15937, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104163981,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.3,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18704, ~N[2025-07-09 19:08:24], 15937]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104164434,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.3,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18704]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104164937,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15937, %{}, 18704, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104166720,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":1.7,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15937]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104167269,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.7,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"email\",\"confirmed_at\",\"customer\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", \"<EMAIL>\", ~U[2025-07-09 19:08:24Z], false, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.4,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303398975\", \"test-workspace--576460752303398975-3b6ula\", %{}, true, 15938, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104169866,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.3,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18705, ~N[2025-07-09 19:08:24], 15938]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104170314,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.3,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18705]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104170859,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.3,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15938, %{}, 18705, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104172838,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":1.8,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15938]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104173264,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303423356\", \"test-workspace--576460752303423356\", %{}, false, 15938, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104173680,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.1,"query":"UPDATE \"workspaces\" SET \"invitations\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [[%{\"code\" => \"Bexu5CpQZ1kJsouMaJDocQ\", \"email\" => \"<EMAIL>\", \"invited_at\" => \"2025-07-09T19:08:24Z\", \"valid_until\" => \"2025-07-16T19:08:24Z\"}], ~N[2025-07-09 19:08:24], 18706]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104174217,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.7,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"email\",\"confirmed_at\",\"customer\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", \"<EMAIL>\", ~U[2025-07-09 19:08:24Z], false, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.3,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303398847\", \"test-workspace--576460752303398847-qrrt6w\", %{}, true, 15939, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104176619,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.2,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18707, ~N[2025-07-09 19:08:24], 15939]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104176920,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.2,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18707]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104177351,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15939, %{}, 18707, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104179387,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":1.9,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15939]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104179817,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303423230\", \"test-workspace--576460752303423230\", %{}, false, 15939, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104180703,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.6,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"email\",\"confirmed_at\",\"customer\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", \"<EMAIL>\", ~U[2025-07-09 19:08:24Z], false, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.3,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303398719\", \"test-workspace--576460752303398719-ivnj2g\", %{}, true, 15940, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104183140,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.2,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18709, ~N[2025-07-09 19:08:24], 15940]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104183502,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.2,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18709]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104184100,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.3,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15940, %{}, 18709, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104186071,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":1.8,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15940]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104186579,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"email\",\"confirmed_at\",\"customer\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", \"<EMAIL>\", ~U[2025-07-09 19:08:24Z], false, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303423100\", \"test-workspace--576460752303423100-pmzbmq\", %{}, true, 15941, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104187708,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.2,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18710, ~N[2025-07-09 19:08:24], 15941]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104187921,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18710]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104188272,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15941, %{}, 18710, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104189298,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.8,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15941]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104189606,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace\", \"test-workspace\", %{}, false, 15941, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104195825,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":6.0,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace\", \"test-workspace\", %{}, false, 15941, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"ERROR"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104196357,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"email\",\"confirmed_at\",\"customer\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", \"<EMAIL>\", ~U[2025-07-09 19:08:24Z], false, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.1,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303423102\", \"test-workspace--576460752303423102-xhkpaa\", %{}, true, 15942, [], ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104197683,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.2,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18713, ~N[2025-07-09 19:08:24], 15942]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104197950,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18713]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104198294,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15942, %{}, 18713, ~N[2025-07-09 19:08:24], ~N[2025-07-09 19:08:24]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088104199002,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.6,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15942]","source":"enabled_features","status":"OK"}}
{"message":"Loading 129 CA(s) from :otp store","timestamp":1752088111000,"level":"info","payload":{"function":"handle_shared_state_initialization/2","line":269,"module":"tls_certificate_check_shared_state","time":1752088111563281,"file":"/workspace/crosspost/deps/tls_certificate_check/src/tls_certificate_check_shared_state.erl","mfa":"{:tls_certificate_check_shared_state, :handle_shared_state_initialization, 2}"}}
{"message":"Migrations already up","timestamp":1752088111000,"level":"info","payload":{"function":"log/2","line":811,"module":"Elixir.Ecto.Migrator","time":1752088111866032,"file":"lib/ecto/migrator.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Migrator, :log, 2}"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112040694,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":1.2,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for Bluesky\", :boolean, \"Support for Bluesky\", \"crossposting_bsky\", false, \"crossposting_bsky\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112041059,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for Mastodon\", :boolean, \"Support for Mastodon\", \"crossposting_mastodon\", false, \"crossposting_mastodon\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112041415,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for LinkedIn\", :boolean, \"Support for LinkedIn\", \"crossposting_linkedin\", false, \"crossposting_linkedin\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112041699,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for X\", :boolean, \"Support for X (Twitter)\", \"crossposting_x\", false, \"crossposting_x\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112042113,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Mastodon\", :boolean, \"Support for Mastodon\", \"crossposting_mastodon\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112042303,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Workspace invites\", :boolean, \"Invite members to workspaces\", \"workspace_invites\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112042548,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Auto scheduler\", :boolean, \"Schedule posts in advance using a beautiful calendar UI\", \"auto_scheduler\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112042892,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Scheduled posts\", :counter, \"Schedule up to 15 posts per month\", \"scheduled_posts_s\", 15, false, \"scheduled_posts\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112043079,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Scheduled posts\", :counter, \"Schedule up to 100 posts per month\", \"scheduled_posts_m\", 100, false, \"scheduled_posts\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112043273,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Scheduled posts\", :counter, \"Schedule unlimited posts\", \"scheduled_posts_l\", -1, false, \"scheduled_posts\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112043442,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Post history\", :limit, \"Access posts from the last 30 days\", \"history_s\", 30, false, \"history\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112043642,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Post history\", :limit, \"Access posts from the last 90 days\", \"history_m\", 90, false, \"history\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112043825,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Post history\", :limit, \"Access posts from the last 12 months\", \"history_l\", 12, false, \"history\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112043963,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic image uploads\", :limit, \"Upload images up to 5MB\", \"image_size_s\", 102400, false, \"image_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112044086,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Enhanced image uploads\", :limit, \"Upload images up to 10MB\", \"image_size_m\", 10485760, false, \"image_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112044372,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Professional image uploads\", :limit, \"Upload images up to 20MB\", \"image_size_l\", 20971520, false, \"image_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112044581,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic video uploads\", :limit, \"Upload videos up to 10MB\", \"video_size_s\", 102400, false, \"video_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112044749,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Enhanced video uploads\", :limit, \"Upload videos up to 50MB\", \"video_size_m\", 52428800, false, \"video_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112044948,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Professional video uploads\", :limit, \"Upload videos up to 100MB\", \"video_size_l\", 104857600, false, \"video_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112045243,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Image optimization\", :boolean, \"Basic image optimization\", \"image_optimization\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112045496,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Video optimization\", :boolean, \"Video optimization for all networks\", \"video_optimization\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112045766,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic @-mentions\", :boolean, \"Basic cross-network mentions\", \"basic_mentions\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112045934,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Advanced @-mentions\", :boolean, \"Advanced cross-network mentions with suggestions\", \"advanced_mentions\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112046247,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic workspace\", :limit, \"1 workspace\", \"workspaces_basic\", 1, false, \"workspaces\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112046435,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Growth workspaces\", :limit, \"3 workspaces\", \"workspaces_growth\", 3, false, \"workspaces\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112046610,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Unlimited workspaces\", :limit, \"Unlimited workspaces with team support\", \"workspaces_unlimited\", -1, false, \"workspaces\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112047019,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic support\", :boolean, \"Email support with 48-hour response time\", \"customer_support\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112047211,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Priority support\", :boolean, \"Email support with 24-hour response time\", \"priority_support\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112047397,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Premium support\", :boolean, \"Priority support with live chat and 12-hour response\", \"premium_support\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112052899,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"plans\" (\"default\",\"enabled\",\"name\",\"description\",\"key\",\"features\",\"highlight\",\"price\",\"stripe_price_id\",\"archived\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12) ON CONFLICT (\"key\") DO UPDATE SET \"features\" = EXCLUDED.\"features\",\"updated_at\" = EXCLUDED.\"updated_at\",\"default\" = EXCLUDED.\"default\" RETURNING \"updated_at\",\"inserted_at\",\"default\",\"archived\",\"features\",\"action\",\"cta\",\"enabled\",\"highlight\",\"stripe_price_id\",\"price\",\"description\",\"name\",\"key\" [false, true, \"Pro\", \"Pro plan\", \"pro_s\", [\"crossposting_bsky\", \"workspaces_basic\", \"scheduled_posts_l\"], false, 1000, \"price_mock123\", false, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112062917,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.0,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":1.0,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"subscription\",\"settings\",\"timezone\",\"customer\",\"plan_key\",\"email\",\"confirmed_at\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11) RETURNING \"id\" [%{}, :active, %Crosspost.Accounts.Subscription{plan: \"pro_s\", status: \"active\", period_start: ~U[2025-06-25 19:08:32Z], period_end: ~U[2025-07-23 19:08:32Z], features: [\"crossposting_bsky\", \"workspaces_basic\", \"scheduled_posts_l\"], stripe_customer_id: nil, stripe_subscription_id: nil, stripe_product_id: nil, stripe_price_id: nil}, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", true, \"pro_s\", \"<EMAIL>\", ~U[2025-07-09 19:08:32Z], ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.0,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.3,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303421183\", \"test-workspace--576460752303421183-evojvq\", %{}, true, 15943, [], ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112069827,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.2,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18714, ~N[2025-07-09 19:08:32], 15943]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112077173,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":2.1,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18714]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112077741,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15943, %{}, 18714, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112079742,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.2,"query":"SELECT p0.\"key\", p0.\"name\", p0.\"description\", p0.\"price\", p0.\"stripe_price_id\", p0.\"highlight\", p0.\"enabled\", p0.\"cta\", p0.\"action\", p0.\"features\", p0.\"archived\", p0.\"default\", p0.\"inserted_at\", p0.\"updated_at\", p0.\"key\" FROM \"plans\" AS p0 WHERE (p0.\"key\" = $1) [\"pro_s\"]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112081466,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":1.1,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15943]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112087741,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":1.0,"query":"INSERT INTO \"users_tokens\" (\"context\",\"token\",\"user_id\",\"inserted_at\") VALUES ($1,$2,$3,$4) RETURNING \"id\" [\"session\", <<8, 93, 209, 177, 26, 215, 184, 57, 111, 78, 22, 139, 85, 182, 181, 8, 107, 171, 179, 211, 61, 157, 240, 7, 127, 77, 122, 150, 202, 216, 221, 0>>, 15943, ~U[2025-07-09 19:08:32Z]]","source":"users_tokens","status":"OK"}}
{"message":"GET /payment/success","timestamp":*************,"level":"info","payload":{"function":"phoenix_endpoint_start/4","line":223,"module":"Elixir.Phoenix.Logger","time":1752088112097736,"file":"lib/phoenix/logger.ex","domain":["elixir"],"application":"phoenix","mfa":"{Phoenix.Logger, :phoenix_endpoint_start, 4}","request_id":"GFCq2uC3FTR72JUAAAnB","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"}}}}
{"message":"Processing with CrosspostWeb.PaymentController.success/2\n  Parameters: %{\"session_id\" => \"test_session\"}\n  Pipelines: [:user_browser, :require_authenticated_user, :workspace]","timestamp":*************,"level":"debug","payload":{"function":"phoenix_router_dispatch_start/4","line":284,"module":"Elixir.Phoenix.Logger","time":1752088112113976,"file":"lib/phoenix/logger.ex","domain":["elixir"],"application":"phoenix","mfa":"{Phoenix.Logger, :phoenix_router_dispatch_start, 4}","request_id":"GFCq2uC3FTR72JUAAAnB","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"}}}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112122735,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"}},"db":0.5,"query":"SELECT u1.\"id\", u1.\"uid\", u1.\"status\", u1.\"customer\", u1.\"email\", u1.\"name\", u1.\"hashed_password\", u1.\"confirmed_at\", u1.\"info\", u1.\"timezone\", u1.\"trial_end\", u1.\"subscription\", u1.\"settings\", u1.\"default_workspace_id\", u1.\"plan_key\", u1.\"inserted_at\", u1.\"updated_at\" FROM \"users_tokens\" AS u0 INNER JOIN \"users\" AS u1 ON u1.\"id\" = u0.\"user_id\" WHERE ((u0.\"token\" = $1) AND (u0.\"context\" = $2)) AND (u0.\"inserted_at\" > $3::timestamp + (-(60)::numeric * interval '1 day')) [<<8, 93, 209, 177, 26, 215, 184, 57, 111, 78, 22, 139, 85, 182, 181, 8, 107, 171, 179, 211, 61, 157, 240, 7, 127, 77, 122, 150, 202, 216, 221, 0>>, \"session\", ~U[2025-07-09 19:08:32.121705Z]]","source":"users_tokens","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112124864,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT p0.\"key\", p0.\"name\", p0.\"description\", p0.\"price\", p0.\"stripe_price_id\", p0.\"highlight\", p0.\"enabled\", p0.\"cta\", p0.\"action\", p0.\"features\", p0.\"archived\", p0.\"default\", p0.\"inserted_at\", p0.\"updated_at\", p0.\"key\" FROM \"plans\" AS p0 WHERE (p0.\"key\" = $1) [\"pro_s\"]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112125333,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.4,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15943]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112127626,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18714]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112129053,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT w0.\"id\", w0.\"workspace_id\", w0.\"user_id\", w0.\"settings\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"user_id\" FROM \"workspace_users\" AS w0 WHERE (w0.\"user_id\" = $1) ORDER BY w0.\"user_id\" [15943]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112129263,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18714]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112131021,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":1.4,"query":"SELECT c0.\"id\", c0.\"platform\", c0.\"platform_user_id\", c0.\"encrypted_access_token\", c0.\"encrypted_refresh_token\", c0.\"expires_at\", c0.\"settings\", c0.\"info\", c0.\"user_id\", c0.\"workspace_id\", c0.\"inserted_at\", c0.\"updated_at\", c0.\"workspace_id\" FROM \"connections\" AS c0 INNER JOIN \"workspace_users\" AS w1 ON (w1.\"workspace_id\" = c0.\"workspace_id\") AND (w1.\"user_id\" = $1) WHERE (c0.\"workspace_id\" = $2) AND (c0.\"workspace_id\" = $3) ORDER BY c0.\"workspace_id\" [15943, 18714, 18714]","source":"connections","status":"OK"}}
{"message":"Processing successful payment","timestamp":*************,"level":"info","payload":{"function":"success/2","line":12,"module":"Elixir.CrosspostWeb.PaymentController","time":1752088112132317,"file":"lib/crosspost_web/controllers/payment_controller.ex","domain":["elixir"],"application":"crosspost","mfa":"{CrosspostWeb.PaymentController, :success, 2}","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}}}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112133749,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.0,"query":"SELECT p0.\"key\", p0.\"name\", p0.\"description\", p0.\"price\", p0.\"stripe_price_id\", p0.\"highlight\", p0.\"enabled\", p0.\"cta\", p0.\"action\", p0.\"features\", p0.\"archived\", p0.\"default\", p0.\"inserted_at\", p0.\"updated_at\" FROM \"plans\" AS p0 WHERE (p0.\"stripe_price_id\" = $1) [\"price_mock123\"]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112134100,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.0,"query":"SELECT u0.\"id\", u0.\"user_id\", u0.\"feature_key\", u0.\"inserted_at\", u0.\"updated_at\", u0.\"user_id\" FROM \"user_features\" AS u0 WHERE (u0.\"user_id\" = $1) ORDER BY u0.\"user_id\" [15943]","source":"user_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112134470,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT f0.\"id\", f0.\"usage_key\", f0.\"type\", f0.\"value\", f0.\"workspace_id\", f0.\"user_id\", f0.\"feature_key\", f0.\"inserted_at\", f0.\"updated_at\", f0.\"user_id\" FROM \"feature_usages\" AS f0 WHERE (f0.\"user_id\" = $1) ORDER BY f0.\"user_id\" [15943]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112137224,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.0,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112137684,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"UPDATE \"users\" SET \"subscription\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [%Crosspost.Accounts.Subscription{plan: \"pro_s\", status: \"active\", period_start: ~U[2025-07-09 19:08:32Z], period_end: ~U[2025-08-08 19:08:32Z], features: [\"crossposting_bsky\", \"workspaces_basic\"], stripe_customer_id: \"cus_mock123\", stripe_subscription_id: \"sub_mock123\", stripe_product_id: \"prod_mock123\", stripe_price_id: \"price_mock123\"}, ~N[2025-07-09 19:08:32], 15943]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.0,"query":"SELECT f0.\"key\", f0.\"usage_key\", f0.\"type\", f0.\"reset_period\", f0.\"limit\", f0.\"name\", f0.\"description\", f0.\"coming_soon\", f0.\"inserted_at\", f0.\"updated_at\" FROM \"features\" AS f0 WHERE (f0.\"key\" = ANY($1)) [[\"crossposting_bsky\", \"workspaces_basic\", \"scheduled_posts_l\"]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112138218,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"red","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"DELETE FROM \"feature_usages\" AS f0 WHERE (((f0.\"user_id\" = $1) AND (f0.\"workspace_id\" = $2)) AND NOT (f0.\"usage_key\" = ANY($3))) [15943, 18714, [\"crossposting_bsky\", \"scheduled_posts\", \"workspaces\"]]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112138608,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.2,"query":"INSERT INTO \"feature_usages\" (\"type\",\"value\",\"user_id\",\"workspace_id\",\"feature_key\",\"usage_key\",\"inserted_at\",\"updated_at\",\"id\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"user_id\",\"workspace_id\",\"usage_key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"feature_key\" = EXCLUDED.\"feature_key\",\"user_id\" = EXCLUDED.\"user_id\",\"workspace_id\" = EXCLUDED.\"workspace_id\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [:boolean, 0, 15943, 18714, \"crossposting_bsky\", \"crossposting_bsky\", ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"07fa307b-5888-4b75-9471-5bbe717cd9c5\"]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112138807,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"INSERT INTO \"feature_usages\" (\"type\",\"value\",\"user_id\",\"workspace_id\",\"feature_key\",\"usage_key\",\"inserted_at\",\"updated_at\",\"id\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"user_id\",\"workspace_id\",\"usage_key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"feature_key\" = EXCLUDED.\"feature_key\",\"user_id\" = EXCLUDED.\"user_id\",\"workspace_id\" = EXCLUDED.\"workspace_id\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [:counter, 0, 15943, 18714, \"scheduled_posts_l\", \"scheduled_posts\", ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"a59d88fa-22ff-420a-a59d-2f61e0057d3e\"]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112139017,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"INSERT INTO \"feature_usages\" (\"type\",\"value\",\"user_id\",\"workspace_id\",\"feature_key\",\"usage_key\",\"inserted_at\",\"updated_at\",\"id\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"user_id\",\"workspace_id\",\"usage_key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"feature_key\" = EXCLUDED.\"feature_key\",\"user_id\" = EXCLUDED.\"user_id\",\"workspace_id\" = EXCLUDED.\"workspace_id\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [:limit, 0, 15943, 18714, \"workspaces_basic\", \"workspaces\", ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"4393bf19-39f9-4cd5-a7b3-0aa73eded7b2\"]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112139239,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"red","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.0,"query":"DELETE FROM \"user_features\" AS u0 WHERE (u0.\"user_id\" = $1) [15943]","source":"user_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112140082,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.6,"query":"INSERT INTO \"user_features\" (\"user_id\",\"inserted_at\",\"updated_at\",\"feature_key\") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12) [15943, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"crossposting_bsky\", 15943, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"scheduled_posts_l\", 15943, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"workspaces_basic\"]","source":"user_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112140191,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.0,"query":"SELECT u0.\"id\", u0.\"user_id\", u0.\"feature_key\", u0.\"inserted_at\", u0.\"updated_at\", u0.\"user_id\" FROM \"user_features\" AS u0 WHERE (u0.\"user_id\" = $1) ORDER BY u0.\"user_id\" [15943]","source":"user_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112140855,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.4,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE ((e0.\"user_id\" = $1) AND (e0.\"feature_type\" = $2)) ORDER BY e0.\"user_id\" [15943, :counter]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112140999,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.0,"query":"SELECT f0.\"id\", f0.\"usage_key\", f0.\"type\", f0.\"value\", f0.\"workspace_id\", f0.\"user_id\", f0.\"feature_key\", f0.\"inserted_at\", f0.\"updated_at\", f0.\"user_id\" FROM \"feature_usages\" AS f0 WHERE (f0.\"user_id\" = $1) ORDER BY f0.\"user_id\" [15943]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112141515,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.4,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15943]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112141744,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT f0.\"key\", f0.\"usage_key\", f0.\"type\", f0.\"reset_period\", f0.\"limit\", f0.\"name\", f0.\"description\", f0.\"coming_soon\", f0.\"inserted_at\", f0.\"updated_at\", f0.\"key\" FROM \"features\" AS f0 WHERE (f0.\"key\" = ANY($1)) [[\"crossposting_bsky\", \"scheduled_posts_l\", \"workspaces_basic\"]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112141878,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.0,"query":"commit []"}}
{"message":"Successfully updated user settings and plan","timestamp":*************,"level":"info","payload":{"function":"update_user_subscription/2","line":71,"module":"Elixir.CrosspostWeb.PaymentController","time":1752088112141891,"file":"lib/crosspost_web/controllers/payment_controller.ex","domain":["elixir"],"application":"crosspost","mfa":"{CrosspostWeb.PaymentController, :update_user_subscription, 2}","features":["crossposting_bsky","workspaces_basic"],"request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","subscription":{"status":"active","features":["crossposting_bsky","workspaces_basic"],"plan":"pro_s","stripe_price_id":"price_mock123","stripe_customer_id":"cus_mock123","stripe_subscription_id":"sub_mock123","stripe_product_id":"prod_mock123","period_end":{"microsecond":"{0, 0}","second":32,"calendar":"Elixir.Calendar.ISO","month":8,"day":8,"year":2025,"minute":8,"hour":19,"time_zone":"Etc/UTC","zone_abbr":"UTC","utc_offset":0,"std_offset":0},"period_start":{"microsecond":"{0, 0}","second":32,"calendar":"Elixir.Calendar.ISO","month":7,"day":9,"year":2025,"minute":8,"hour":19,"time_zone":"Etc/UTC","zone_abbr":"UTC","utc_offset":0,"std_offset":0}},"plan_key":"pro_s","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}}}}
{"message":"Successfully updated user subscription","timestamp":*************,"level":"info","payload":{"function":"success/2","line":16,"module":"Elixir.CrosspostWeb.PaymentController","time":1752088112141912,"file":"lib/crosspost_web/controllers/payment_controller.ex","domain":["elixir"],"application":"crosspost","mfa":"{CrosspostWeb.PaymentController, :success, 2}","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","subscription":{"status":"active","features":["crossposting_bsky","workspaces_basic"],"plan":"pro_s","stripe_price_id":"price_mock123","stripe_customer_id":"cus_mock123","stripe_subscription_id":"sub_mock123","stripe_product_id":"prod_mock123","period_end":{"microsecond":"{0, 0}","second":32,"calendar":"Elixir.Calendar.ISO","month":8,"day":8,"year":2025,"minute":8,"hour":19,"time_zone":"Etc/UTC","zone_abbr":"UTC","utc_offset":0,"std_offset":0},"period_start":{"microsecond":"{0, 0}","second":32,"calendar":"Elixir.Calendar.ISO","month":7,"day":9,"year":2025,"minute":8,"hour":19,"time_zone":"Etc/UTC","zone_abbr":"UTC","utc_offset":0,"std_offset":0}},"__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}}}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112164094,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT u0.\"id\", u0.\"uid\", u0.\"status\", u0.\"customer\", u0.\"email\", u0.\"name\", u0.\"hashed_password\", u0.\"confirmed_at\", u0.\"info\", u0.\"timezone\", u0.\"trial_end\", u0.\"subscription\", u0.\"settings\", u0.\"default_workspace_id\", u0.\"plan_key\", u0.\"inserted_at\", u0.\"updated_at\", w1.\"settings\" FROM \"users\" AS u0 LEFT OUTER JOIN \"workspace_users\" AS w1 ON (w1.\"user_id\" = u0.\"id\") AND (w1.\"workspace_id\" = $1) WHERE (u0.\"id\" = $2) [18714, 15943]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112165059,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.6,"query":"SELECT s0.\"id\", s0.\"name\", s0.\"bsky_handle\", s0.\"x_handle\", s0.\"mastodon_handle\", s0.\"user_id\", s0.\"inserted_at\", s0.\"updated_at\", s0.\"user_id\" FROM \"social_profiles\" AS s0 WHERE (s0.\"user_id\" = $1) ORDER BY s0.\"user_id\" [15943]","source":"social_profiles","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112165239,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT p0.\"key\", p0.\"name\", p0.\"description\", p0.\"price\", p0.\"stripe_price_id\", p0.\"highlight\", p0.\"enabled\", p0.\"cta\", p0.\"action\", p0.\"features\", p0.\"archived\", p0.\"default\", p0.\"inserted_at\", p0.\"updated_at\", p0.\"key\" FROM \"plans\" AS p0 WHERE (p0.\"key\" = $1) [\"pro_s\"]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112165800,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.5,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15943]","source":"enabled_features","status":"OK"}}
{"message":"Sent 200 in 89ms","timestamp":*************,"level":"info","payload":{"function":"phoenix_endpoint_stop/4","line":237,"module":"Elixir.Phoenix.Logger","time":1752088112186987,"file":"lib/phoenix/logger.ex","domain":["elixir"],"application":"phoenix","mfa":"{Phoenix.Logger, :phoenix_endpoint_stop, 4}","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}}}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112188484,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT u0.\"id\", u0.\"uid\", u0.\"status\", u0.\"customer\", u0.\"email\", u0.\"name\", u0.\"hashed_password\", u0.\"confirmed_at\", u0.\"info\", u0.\"timezone\", u0.\"trial_end\", u0.\"subscription\", u0.\"settings\", u0.\"default_workspace_id\", u0.\"plan_key\", u0.\"inserted_at\", u0.\"updated_at\" FROM \"users\" AS u0 WHERE (u0.\"id\" = $1) [15943]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112188701,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT u0.\"id\", u0.\"user_id\", u0.\"feature_key\", u0.\"inserted_at\", u0.\"updated_at\", u0.\"user_id\" FROM \"user_features\" AS u0 WHERE (u0.\"user_id\" = $1) ORDER BY u0.\"user_id\" [15943]","source":"user_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112188835,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT p0.\"key\", p0.\"name\", p0.\"description\", p0.\"price\", p0.\"stripe_price_id\", p0.\"highlight\", p0.\"enabled\", p0.\"cta\", p0.\"action\", p0.\"features\", p0.\"archived\", p0.\"default\", p0.\"inserted_at\", p0.\"updated_at\", p0.\"key\" FROM \"plans\" AS p0 WHERE (p0.\"key\" = $1) [\"pro_s\"]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112189051,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uC3FTR72JUAAAnB","user_id":15943,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15943,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT f0.\"key\", f0.\"usage_key\", f0.\"type\", f0.\"reset_period\", f0.\"limit\", f0.\"name\", f0.\"description\", f0.\"coming_soon\", f0.\"inserted_at\", f0.\"updated_at\", f0.\"key\" FROM \"features\" AS f0 WHERE (f0.\"key\" = ANY($1)) [[\"crossposting_bsky\", \"scheduled_posts_l\", \"workspaces_basic\"]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112193217,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for Bluesky\", :boolean, \"Support for Bluesky\", \"crossposting_bsky\", false, \"crossposting_bsky\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112193620,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for Mastodon\", :boolean, \"Support for Mastodon\", \"crossposting_mastodon\", false, \"crossposting_mastodon\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112193821,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for LinkedIn\", :boolean, \"Support for LinkedIn\", \"crossposting_linkedin\", false, \"crossposting_linkedin\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112194003,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for X\", :boolean, \"Support for X (Twitter)\", \"crossposting_x\", false, \"crossposting_x\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112194419,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Mastodon\", :boolean, \"Support for Mastodon\", \"crossposting_mastodon\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112194642,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Workspace invites\", :boolean, \"Invite members to workspaces\", \"workspace_invites\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112194785,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Auto scheduler\", :boolean, \"Schedule posts in advance using a beautiful calendar UI\", \"auto_scheduler\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112195062,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Scheduled posts\", :counter, \"Schedule up to 15 posts per month\", \"scheduled_posts_s\", 15, false, \"scheduled_posts\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112195223,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Scheduled posts\", :counter, \"Schedule up to 100 posts per month\", \"scheduled_posts_m\", 100, false, \"scheduled_posts\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112195397,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Scheduled posts\", :counter, \"Schedule unlimited posts\", \"scheduled_posts_l\", -1, false, \"scheduled_posts\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112195606,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Post history\", :limit, \"Access posts from the last 30 days\", \"history_s\", 30, false, \"history\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112195861,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Post history\", :limit, \"Access posts from the last 90 days\", \"history_m\", 90, false, \"history\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112196013,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Post history\", :limit, \"Access posts from the last 12 months\", \"history_l\", 12, false, \"history\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112196225,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic image uploads\", :limit, \"Upload images up to 5MB\", \"image_size_s\", 102400, false, \"image_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112196356,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Enhanced image uploads\", :limit, \"Upload images up to 10MB\", \"image_size_m\", 10485760, false, \"image_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112196486,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Professional image uploads\", :limit, \"Upload images up to 20MB\", \"image_size_l\", 20971520, false, \"image_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112196602,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic video uploads\", :limit, \"Upload videos up to 10MB\", \"video_size_s\", 102400, false, \"video_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112196735,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Enhanced video uploads\", :limit, \"Upload videos up to 50MB\", \"video_size_m\", 52428800, false, \"video_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112196888,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Professional video uploads\", :limit, \"Upload videos up to 100MB\", \"video_size_l\", 104857600, false, \"video_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112197124,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Image optimization\", :boolean, \"Basic image optimization\", \"image_optimization\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112197276,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Video optimization\", :boolean, \"Video optimization for all networks\", \"video_optimization\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112197490,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic @-mentions\", :boolean, \"Basic cross-network mentions\", \"basic_mentions\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112197685,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Advanced @-mentions\", :boolean, \"Advanced cross-network mentions with suggestions\", \"advanced_mentions\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112197966,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic workspace\", :limit, \"1 workspace\", \"workspaces_basic\", 1, false, \"workspaces\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112198151,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Growth workspaces\", :limit, \"3 workspaces\", \"workspaces_growth\", 3, false, \"workspaces\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112198319,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Unlimited workspaces\", :limit, \"Unlimited workspaces with team support\", \"workspaces_unlimited\", -1, false, \"workspaces\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112198616,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic support\", :boolean, \"Email support with 48-hour response time\", \"customer_support\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112198772,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Priority support\", :boolean, \"Email support with 24-hour response time\", \"priority_support\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112198941,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Premium support\", :boolean, \"Priority support with live chat and 12-hour response\", \"premium_support\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112199902,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"plans\" (\"default\",\"enabled\",\"name\",\"description\",\"key\",\"features\",\"highlight\",\"price\",\"stripe_price_id\",\"archived\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12) ON CONFLICT (\"key\") DO UPDATE SET \"features\" = EXCLUDED.\"features\",\"updated_at\" = EXCLUDED.\"updated_at\",\"default\" = EXCLUDED.\"default\" RETURNING \"updated_at\",\"inserted_at\",\"default\",\"archived\",\"features\",\"action\",\"cta\",\"enabled\",\"highlight\",\"stripe_price_id\",\"price\",\"description\",\"name\",\"key\" [false, true, \"Pro\", \"Pro plan\", \"pro_s\", [\"crossposting_bsky\", \"workspaces_basic\", \"scheduled_posts_l\"], false, 1000, \"price_mock123\", false, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"plans","status":"OK"}}
{"message":"GET /payment/success","timestamp":*************,"level":"info","payload":{"function":"phoenix_endpoint_start/4","line":223,"module":"Elixir.Phoenix.Logger","time":1752088112199969,"file":"lib/phoenix/logger.ex","domain":["elixir"],"application":"phoenix","mfa":"{Phoenix.Logger, :phoenix_endpoint_start, 4}","request_id":"GFCq2ubc_fE45GkAAEgD","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"}}}}
{"message":"Processing with CrosspostWeb.PaymentController.success/2\n  Parameters: %{\"session_id\" => \"test_session\"}\n  Pipelines: [:user_browser, :require_authenticated_user, :workspace]","timestamp":*************,"level":"debug","payload":{"function":"phoenix_router_dispatch_start/4","line":284,"module":"Elixir.Phoenix.Logger","time":1752088112200031,"file":"lib/phoenix/logger.ex","domain":["elixir"],"application":"phoenix","mfa":"{Phoenix.Logger, :phoenix_router_dispatch_start, 4}","request_id":"GFCq2ubc_fE45GkAAEgD","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"}}}}
{"message":"Sent 302 in 127µs","timestamp":*************,"level":"info","payload":{"function":"phoenix_endpoint_stop/4","line":237,"module":"Elixir.Phoenix.Logger","time":1752088112200095,"file":"lib/phoenix/logger.ex","domain":["elixir"],"application":"phoenix","mfa":"{Phoenix.Logger, :phoenix_endpoint_stop, 4}","request_id":"GFCq2ubc_fE45GkAAEgD","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"}}}}
{"message":"Phoenix.Router halted in :require_authenticated_user/2","timestamp":*************,"level":"debug","payload":{"function":"__pipe_through3__/1","line":1,"module":"Elixir.CrosspostWeb.Router","time":1752088112200113,"file":"lib/crosspost_web/router.ex","domain":["elixir"],"application":"crosspost","mfa":"{CrosspostWeb.Router, :__pipe_through3__, 1}","request_id":"GFCq2ubc_fE45GkAAEgD","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"}}}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112204081,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for Bluesky\", :boolean, \"Support for Bluesky\", \"crossposting_bsky\", false, \"crossposting_bsky\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112204307,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for Mastodon\", :boolean, \"Support for Mastodon\", \"crossposting_mastodon\", false, \"crossposting_mastodon\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112204512,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for LinkedIn\", :boolean, \"Support for LinkedIn\", \"crossposting_linkedin\", false, \"crossposting_linkedin\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112204764,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for X\", :boolean, \"Support for X (Twitter)\", \"crossposting_x\", false, \"crossposting_x\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112205113,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Mastodon\", :boolean, \"Support for Mastodon\", \"crossposting_mastodon\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112205335,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Workspace invites\", :boolean, \"Invite members to workspaces\", \"workspace_invites\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112205507,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Auto scheduler\", :boolean, \"Schedule posts in advance using a beautiful calendar UI\", \"auto_scheduler\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112205791,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Scheduled posts\", :counter, \"Schedule up to 15 posts per month\", \"scheduled_posts_s\", 15, false, \"scheduled_posts\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112205967,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Scheduled posts\", :counter, \"Schedule up to 100 posts per month\", \"scheduled_posts_m\", 100, false, \"scheduled_posts\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112206124,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Scheduled posts\", :counter, \"Schedule unlimited posts\", \"scheduled_posts_l\", -1, false, \"scheduled_posts\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112206424,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Post history\", :limit, \"Access posts from the last 30 days\", \"history_s\", 30, false, \"history\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112206653,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Post history\", :limit, \"Access posts from the last 90 days\", \"history_m\", 90, false, \"history\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112206874,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Post history\", :limit, \"Access posts from the last 12 months\", \"history_l\", 12, false, \"history\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112207023,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic image uploads\", :limit, \"Upload images up to 5MB\", \"image_size_s\", 102400, false, \"image_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112207186,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Enhanced image uploads\", :limit, \"Upload images up to 10MB\", \"image_size_m\", 10485760, false, \"image_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112207330,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Professional image uploads\", :limit, \"Upload images up to 20MB\", \"image_size_l\", 20971520, false, \"image_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112207507,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic video uploads\", :limit, \"Upload videos up to 10MB\", \"video_size_s\", 102400, false, \"video_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112207641,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Enhanced video uploads\", :limit, \"Upload videos up to 50MB\", \"video_size_m\", 52428800, false, \"video_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112207842,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Professional video uploads\", :limit, \"Upload videos up to 100MB\", \"video_size_l\", 104857600, false, \"video_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112208099,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Image optimization\", :boolean, \"Basic image optimization\", \"image_optimization\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112208290,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Video optimization\", :boolean, \"Video optimization for all networks\", \"video_optimization\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112208549,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic @-mentions\", :boolean, \"Basic cross-network mentions\", \"basic_mentions\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112208771,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Advanced @-mentions\", :boolean, \"Advanced cross-network mentions with suggestions\", \"advanced_mentions\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112209090,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic workspace\", :limit, \"1 workspace\", \"workspaces_basic\", 1, false, \"workspaces\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112209262,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Growth workspaces\", :limit, \"3 workspaces\", \"workspaces_growth\", 3, false, \"workspaces\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112209435,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Unlimited workspaces\", :limit, \"Unlimited workspaces with team support\", \"workspaces_unlimited\", -1, false, \"workspaces\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112209727,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic support\", :boolean, \"Email support with 48-hour response time\", \"customer_support\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112209897,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Priority support\", :boolean, \"Email support with 24-hour response time\", \"priority_support\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112210096,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Premium support\", :boolean, \"Priority support with live chat and 12-hour response\", \"premium_support\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112211089,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"plans\" (\"default\",\"enabled\",\"name\",\"description\",\"key\",\"features\",\"highlight\",\"price\",\"stripe_price_id\",\"archived\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12) ON CONFLICT (\"key\") DO UPDATE SET \"features\" = EXCLUDED.\"features\",\"updated_at\" = EXCLUDED.\"updated_at\",\"default\" = EXCLUDED.\"default\" RETURNING \"updated_at\",\"inserted_at\",\"default\",\"archived\",\"features\",\"action\",\"cta\",\"enabled\",\"highlight\",\"stripe_price_id\",\"price\",\"description\",\"name\",\"key\" [false, true, \"Pro\", \"Pro plan\", \"pro_s\", [\"crossposting_bsky\", \"workspaces_basic\", \"scheduled_posts_l\"], false, 1000, \"price_mock123\", false, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112211413,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"plans\" (\"default\",\"enabled\",\"name\",\"description\",\"key\",\"features\",\"highlight\",\"price\",\"stripe_price_id\",\"archived\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12) ON CONFLICT (\"key\") DO UPDATE SET \"features\" = EXCLUDED.\"features\",\"updated_at\" = EXCLUDED.\"updated_at\",\"default\" = EXCLUDED.\"default\" RETURNING \"updated_at\",\"inserted_at\",\"default\",\"archived\",\"features\",\"action\",\"cta\",\"enabled\",\"highlight\",\"stripe_price_id\",\"price\",\"description\",\"name\",\"key\" [false, true, \"Basic\", \"Basic plan\", \"basic\", [\"scheduled_posts_s\", \"workspaces_basic\"], false, 300, \"price_basic123\", false, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112211581,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.0,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.8,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"customer\",\"plan_key\",\"email\",\"confirmed_at\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", false, \"basic\", \"<EMAIL>\", ~U[2025-07-09 19:08:32Z], ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.0,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303420734\", \"test-workspace--576460752303420734-dcdx8g\", %{}, true, 15944, [], ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112213761,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.2,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18715, ~N[2025-07-09 19:08:32], 15944]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112214040,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.2,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18715]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112214526,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15944, %{}, 18715, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112215239,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.5,"query":"SELECT p0.\"key\", p0.\"name\", p0.\"description\", p0.\"price\", p0.\"stripe_price_id\", p0.\"highlight\", p0.\"enabled\", p0.\"cta\", p0.\"action\", p0.\"features\", p0.\"archived\", p0.\"default\", p0.\"inserted_at\", p0.\"updated_at\", p0.\"key\" FROM \"plans\" AS p0 WHERE (p0.\"key\" = $1) [\"basic\"]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112216753,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":1.4,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15944]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112217137,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18715]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112217328,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18715]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112217615,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT f0.\"key\", f0.\"usage_key\", f0.\"type\", f0.\"reset_period\", f0.\"limit\", f0.\"name\", f0.\"description\", f0.\"coming_soon\", f0.\"inserted_at\", f0.\"updated_at\" FROM \"features\" AS f0 WHERE (f0.\"key\" = $1) [\"scheduled_posts_l\"]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112225199,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"feature_usages\" (\"type\",\"value\",\"user_id\",\"workspace_id\",\"feature_key\",\"usage_key\",\"inserted_at\",\"updated_at\",\"id\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"user_id\",\"workspace_id\",\"usage_key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"feature_key\" = EXCLUDED.\"feature_key\",\"user_id\" = EXCLUDED.\"user_id\",\"workspace_id\" = EXCLUDED.\"workspace_id\",\"value\" = EXCLUDED.\"value\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [:counter, 10, 15944, 18715, \"scheduled_posts_s\", \"scheduled_posts\", ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"e2f816a1-610c-414a-b0f3-d78dd5c74d3b\"]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112225699,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.2,"query":"SELECT f0.\"id\", f0.\"usage_key\", f0.\"type\", f0.\"value\", f0.\"workspace_id\", f0.\"user_id\", f0.\"feature_key\", f0.\"inserted_at\", f0.\"updated_at\" FROM \"feature_usages\" AS f0 WHERE (((f0.\"user_id\" = $1) AND (f0.\"workspace_id\" = $2)) AND (f0.\"usage_key\" = $3)) [15944, 18715, \"scheduled_posts\"]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112226237,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"users_tokens\" (\"context\",\"token\",\"user_id\",\"inserted_at\") VALUES ($1,$2,$3,$4) RETURNING \"id\" [\"session\", <<14, 113, 33, 206, 120, 70, 123, 24, 144, 223, 210, 152, 189, 161, 234, 28, 60, 202, 4, 148, 214, 93, 105, 171, 48, 115, 92, 146, 199, 4, 116, 144>>, 15944, ~U[2025-07-09 19:08:32Z]]","source":"users_tokens","status":"OK"}}
{"message":"GET /payment/success","timestamp":*************,"level":"info","payload":{"function":"phoenix_endpoint_start/4","line":223,"module":"Elixir.Phoenix.Logger","time":1752088112226356,"file":"lib/phoenix/logger.ex","domain":["elixir"],"application":"phoenix","mfa":"{Phoenix.Logger, :phoenix_endpoint_start, 4}","request_id":"GFCq2uhvjxJdq9wAAEmD","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"}}}}
{"message":"Processing with CrosspostWeb.PaymentController.success/2\n  Parameters: %{\"session_id\" => \"test_session\"}\n  Pipelines: [:user_browser, :require_authenticated_user, :workspace]","timestamp":*************,"level":"debug","payload":{"function":"phoenix_router_dispatch_start/4","line":284,"module":"Elixir.Phoenix.Logger","time":1752088112226425,"file":"lib/phoenix/logger.ex","domain":["elixir"],"application":"phoenix","mfa":"{Phoenix.Logger, :phoenix_router_dispatch_start, 4}","request_id":"GFCq2uhvjxJdq9wAAEmD","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"}}}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112227164,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"}},"db":0.5,"query":"SELECT u1.\"id\", u1.\"uid\", u1.\"status\", u1.\"customer\", u1.\"email\", u1.\"name\", u1.\"hashed_password\", u1.\"confirmed_at\", u1.\"info\", u1.\"timezone\", u1.\"trial_end\", u1.\"subscription\", u1.\"settings\", u1.\"default_workspace_id\", u1.\"plan_key\", u1.\"inserted_at\", u1.\"updated_at\" FROM \"users_tokens\" AS u0 INNER JOIN \"users\" AS u1 ON u1.\"id\" = u0.\"user_id\" WHERE ((u0.\"token\" = $1) AND (u0.\"context\" = $2)) AND (u0.\"inserted_at\" > $3::timestamp + (-(60)::numeric * interval '1 day')) [<<14, 113, 33, 206, 120, 70, 123, 24, 144, 223, 210, 152, 189, 161, 234, 28, 60, 202, 4, 148, 214, 93, 105, 171, 48, 115, 92, 146, 199, 4, 116, 144>>, \"session\", ~U[2025-07-09 19:08:32.226467Z]]","source":"users_tokens","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112227511,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT p0.\"key\", p0.\"name\", p0.\"description\", p0.\"price\", p0.\"stripe_price_id\", p0.\"highlight\", p0.\"enabled\", p0.\"cta\", p0.\"action\", p0.\"features\", p0.\"archived\", p0.\"default\", p0.\"inserted_at\", p0.\"updated_at\", p0.\"key\" FROM \"plans\" AS p0 WHERE (p0.\"key\" = $1) [\"basic\"]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112228207,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.6,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15944]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112228439,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18715]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112228749,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.2,"query":"SELECT w0.\"id\", w0.\"workspace_id\", w0.\"user_id\", w0.\"settings\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"user_id\" FROM \"workspace_users\" AS w0 WHERE (w0.\"user_id\" = $1) ORDER BY w0.\"user_id\" [15944]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112228913,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.0,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18715]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112229448,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.4,"query":"SELECT c0.\"id\", c0.\"platform\", c0.\"platform_user_id\", c0.\"encrypted_access_token\", c0.\"encrypted_refresh_token\", c0.\"expires_at\", c0.\"settings\", c0.\"info\", c0.\"user_id\", c0.\"workspace_id\", c0.\"inserted_at\", c0.\"updated_at\", c0.\"workspace_id\" FROM \"connections\" AS c0 INNER JOIN \"workspace_users\" AS w1 ON (w1.\"workspace_id\" = c0.\"workspace_id\") AND (w1.\"user_id\" = $1) WHERE (c0.\"workspace_id\" = $2) AND (c0.\"workspace_id\" = $3) ORDER BY c0.\"workspace_id\" [15944, 18715, 18715]","source":"connections","status":"OK"}}
{"message":"Processing successful payment","timestamp":*************,"level":"info","payload":{"function":"success/2","line":12,"module":"Elixir.CrosspostWeb.PaymentController","time":1752088112229497,"file":"lib/crosspost_web/controllers/payment_controller.ex","domain":["elixir"],"application":"crosspost","mfa":"{CrosspostWeb.PaymentController, :success, 2}","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}}}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112229845,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.2,"query":"SELECT p0.\"key\", p0.\"name\", p0.\"description\", p0.\"price\", p0.\"stripe_price_id\", p0.\"highlight\", p0.\"enabled\", p0.\"cta\", p0.\"action\", p0.\"features\", p0.\"archived\", p0.\"default\", p0.\"inserted_at\", p0.\"updated_at\" FROM \"plans\" AS p0 WHERE (p0.\"stripe_price_id\" = $1) [\"price_mock123\"]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112230197,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.2,"query":"SELECT u0.\"id\", u0.\"user_id\", u0.\"feature_key\", u0.\"inserted_at\", u0.\"updated_at\", u0.\"user_id\" FROM \"user_features\" AS u0 WHERE (u0.\"user_id\" = $1) ORDER BY u0.\"user_id\" [15944]","source":"user_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112230463,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.2,"query":"SELECT f0.\"id\", f0.\"usage_key\", f0.\"type\", f0.\"value\", f0.\"workspace_id\", f0.\"user_id\", f0.\"feature_key\", f0.\"inserted_at\", f0.\"updated_at\", f0.\"user_id\" FROM \"feature_usages\" AS f0 WHERE (f0.\"user_id\" = $1) ORDER BY f0.\"user_id\" [15944]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112230591,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.0,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112230998,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.2,"query":"UPDATE \"users\" SET \"status\" = $1, \"subscription\" = $2, \"customer\" = $3, \"plan_key\" = $4, \"updated_at\" = $5 WHERE \"id\" = $6 [:active, %Crosspost.Accounts.Subscription{plan: \"pro_s\", status: \"active\", period_start: ~U[2025-07-09 19:08:32Z], period_end: ~U[2025-08-08 19:08:32Z], features: [\"crossposting\", \"workspaces_basic\", \"scheduled_posts_l\"], stripe_customer_id: \"cus_mock123\", stripe_subscription_id: \"sub_mock123\", stripe_product_id: \"prod_mock123\", stripe_price_id: \"price_mock123\"}, true, \"pro_s\", ~N[2025-07-09 19:08:32], 15944]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT f0.\"key\", f0.\"usage_key\", f0.\"type\", f0.\"reset_period\", f0.\"limit\", f0.\"name\", f0.\"description\", f0.\"coming_soon\", f0.\"inserted_at\", f0.\"updated_at\" FROM \"features\" AS f0 WHERE (f0.\"key\" = ANY($1)) [[\"crossposting_bsky\", \"workspaces_basic\", \"scheduled_posts_l\"]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112231372,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.0,"query":"SELECT p0.\"key\", p0.\"name\", p0.\"description\", p0.\"price\", p0.\"stripe_price_id\", p0.\"highlight\", p0.\"enabled\", p0.\"cta\", p0.\"action\", p0.\"features\", p0.\"archived\", p0.\"default\", p0.\"inserted_at\", p0.\"updated_at\", p0.\"key\" FROM \"plans\" AS p0 WHERE (p0.\"key\" = $1) [\"pro_s\"]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112231634,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"red","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.2,"query":"DELETE FROM \"feature_usages\" AS f0 WHERE (((f0.\"user_id\" = $1) AND (f0.\"workspace_id\" = $2)) AND NOT (f0.\"usage_key\" = ANY($3))) [15944, 18715, [\"crossposting_bsky\", \"scheduled_posts\", \"workspaces\"]]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112231913,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"INSERT INTO \"feature_usages\" (\"type\",\"value\",\"user_id\",\"workspace_id\",\"feature_key\",\"usage_key\",\"inserted_at\",\"updated_at\",\"id\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"user_id\",\"workspace_id\",\"usage_key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"feature_key\" = EXCLUDED.\"feature_key\",\"user_id\" = EXCLUDED.\"user_id\",\"workspace_id\" = EXCLUDED.\"workspace_id\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [:boolean, 0, 15944, 18715, \"crossposting_bsky\", \"crossposting_bsky\", ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"efbe0f6f-1830-48ba-9f95-ffd5e34c63ab\"]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112232121,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"INSERT INTO \"feature_usages\" (\"type\",\"value\",\"user_id\",\"workspace_id\",\"feature_key\",\"usage_key\",\"inserted_at\",\"updated_at\",\"id\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"user_id\",\"workspace_id\",\"usage_key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"feature_key\" = EXCLUDED.\"feature_key\",\"user_id\" = EXCLUDED.\"user_id\",\"workspace_id\" = EXCLUDED.\"workspace_id\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [:counter, 10, 15944, 18715, \"scheduled_posts_l\", \"scheduled_posts\", ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"65fd1dff-fcfe-45f1-a90f-d93678cce9db\"]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112232328,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"INSERT INTO \"feature_usages\" (\"type\",\"value\",\"user_id\",\"workspace_id\",\"feature_key\",\"usage_key\",\"inserted_at\",\"updated_at\",\"id\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"user_id\",\"workspace_id\",\"usage_key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"feature_key\" = EXCLUDED.\"feature_key\",\"user_id\" = EXCLUDED.\"user_id\",\"workspace_id\" = EXCLUDED.\"workspace_id\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [:limit, 0, 15944, 18715, \"workspaces_basic\", \"workspaces\", ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"1b6a7261-4749-45cb-a64e-011b73bb0be9\"]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112232516,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"red","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"DELETE FROM \"user_features\" AS u0 WHERE (u0.\"user_id\" = $1) [15944]","source":"user_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112232899,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.2,"query":"INSERT INTO \"user_features\" (\"user_id\",\"inserted_at\",\"updated_at\",\"feature_key\") VALUES ($1,$2,$3,$4),($5,$6,$7,$8),($9,$10,$11,$12) [15944, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"crossposting_bsky\", 15944, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"scheduled_posts_l\", 15944, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32], \"workspaces_basic\"]","source":"user_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112233012,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.0,"query":"SELECT u0.\"id\", u0.\"user_id\", u0.\"feature_key\", u0.\"inserted_at\", u0.\"updated_at\", u0.\"user_id\" FROM \"user_features\" AS u0 WHERE (u0.\"user_id\" = $1) ORDER BY u0.\"user_id\" [15944]","source":"user_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112233700,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.6,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE ((e0.\"user_id\" = $1) AND (e0.\"feature_type\" = $2)) ORDER BY e0.\"user_id\" [15944, :counter]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112233849,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT f0.\"id\", f0.\"usage_key\", f0.\"type\", f0.\"value\", f0.\"workspace_id\", f0.\"user_id\", f0.\"feature_key\", f0.\"inserted_at\", f0.\"updated_at\", f0.\"user_id\" FROM \"feature_usages\" AS f0 WHERE (f0.\"user_id\" = $1) ORDER BY f0.\"user_id\" [15944]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112234468,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.5,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15944]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112234748,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.2,"query":"SELECT f0.\"key\", f0.\"usage_key\", f0.\"type\", f0.\"reset_period\", f0.\"limit\", f0.\"name\", f0.\"description\", f0.\"coming_soon\", f0.\"inserted_at\", f0.\"updated_at\", f0.\"key\" FROM \"features\" AS f0 WHERE (f0.\"key\" = ANY($1)) [[\"crossposting_bsky\", \"scheduled_posts_l\", \"workspaces_basic\"]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112234882,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.0,"query":"commit []"}}
{"message":"Successfully updated user settings and plan","timestamp":*************,"level":"info","payload":{"function":"update_user_subscription/2","line":71,"module":"Elixir.CrosspostWeb.PaymentController","time":1752088112234899,"file":"lib/crosspost_web/controllers/payment_controller.ex","domain":["elixir"],"application":"crosspost","mfa":"{CrosspostWeb.PaymentController, :update_user_subscription, 2}","features":["crossposting","workspaces_basic","scheduled_posts_l"],"request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","subscription":{"status":"active","features":["crossposting","workspaces_basic","scheduled_posts_l"],"plan":"pro_s","stripe_price_id":"price_mock123","stripe_customer_id":"cus_mock123","stripe_subscription_id":"sub_mock123","stripe_product_id":"prod_mock123","period_end":{"microsecond":"{0, 0}","second":32,"calendar":"Elixir.Calendar.ISO","month":8,"day":8,"year":2025,"minute":8,"hour":19,"time_zone":"Etc/UTC","zone_abbr":"UTC","utc_offset":0,"std_offset":0},"period_start":{"microsecond":"{0, 0}","second":32,"calendar":"Elixir.Calendar.ISO","month":7,"day":9,"year":2025,"minute":8,"hour":19,"time_zone":"Etc/UTC","zone_abbr":"UTC","utc_offset":0,"std_offset":0}},"plan_key":"pro_s","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}}}}
{"message":"Successfully updated user subscription","timestamp":*************,"level":"info","payload":{"function":"success/2","line":16,"module":"Elixir.CrosspostWeb.PaymentController","time":1752088112234919,"file":"lib/crosspost_web/controllers/payment_controller.ex","domain":["elixir"],"application":"crosspost","mfa":"{CrosspostWeb.PaymentController, :success, 2}","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","subscription":{"status":"active","features":["crossposting","workspaces_basic","scheduled_posts_l"],"plan":"pro_s","stripe_price_id":"price_mock123","stripe_customer_id":"cus_mock123","stripe_subscription_id":"sub_mock123","stripe_product_id":"prod_mock123","period_end":{"microsecond":"{0, 0}","second":32,"calendar":"Elixir.Calendar.ISO","month":8,"day":8,"year":2025,"minute":8,"hour":19,"time_zone":"Etc/UTC","zone_abbr":"UTC","utc_offset":0,"std_offset":0},"period_start":{"microsecond":"{0, 0}","second":32,"calendar":"Elixir.Calendar.ISO","month":7,"day":9,"year":2025,"minute":8,"hour":19,"time_zone":"Etc/UTC","zone_abbr":"UTC","utc_offset":0,"std_offset":0}},"__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}}}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112235345,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.3,"query":"SELECT u0.\"id\", u0.\"uid\", u0.\"status\", u0.\"customer\", u0.\"email\", u0.\"name\", u0.\"hashed_password\", u0.\"confirmed_at\", u0.\"info\", u0.\"timezone\", u0.\"trial_end\", u0.\"subscription\", u0.\"settings\", u0.\"default_workspace_id\", u0.\"plan_key\", u0.\"inserted_at\", u0.\"updated_at\", w1.\"settings\" FROM \"users\" AS u0 LEFT OUTER JOIN \"workspace_users\" AS w1 ON (w1.\"user_id\" = u0.\"id\") AND (w1.\"workspace_id\" = $1) WHERE (u0.\"id\" = $2) [18715, 15944]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112235790,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.3,"query":"SELECT s0.\"id\", s0.\"name\", s0.\"bsky_handle\", s0.\"x_handle\", s0.\"mastodon_handle\", s0.\"user_id\", s0.\"inserted_at\", s0.\"updated_at\", s0.\"user_id\" FROM \"social_profiles\" AS s0 WHERE (s0.\"user_id\" = $1) ORDER BY s0.\"user_id\" [15944]","source":"social_profiles","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112235906,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT p0.\"key\", p0.\"name\", p0.\"description\", p0.\"price\", p0.\"stripe_price_id\", p0.\"highlight\", p0.\"enabled\", p0.\"cta\", p0.\"action\", p0.\"features\", p0.\"archived\", p0.\"default\", p0.\"inserted_at\", p0.\"updated_at\", p0.\"key\" FROM \"plans\" AS p0 WHERE (p0.\"key\" = $1) [\"pro_s\"]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112236429,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.4,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15944]","source":"enabled_features","status":"OK"}}
{"message":"Sent 200 in 10ms","timestamp":*************,"level":"info","payload":{"function":"phoenix_endpoint_stop/4","line":237,"module":"Elixir.Phoenix.Logger","time":1752088112236730,"file":"lib/phoenix/logger.ex","domain":["elixir"],"application":"phoenix","mfa":"{Phoenix.Logger, :phoenix_endpoint_stop, 4}","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}}}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112237175,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.2,"query":"SELECT u0.\"id\", u0.\"uid\", u0.\"status\", u0.\"customer\", u0.\"email\", u0.\"name\", u0.\"hashed_password\", u0.\"confirmed_at\", u0.\"info\", u0.\"timezone\", u0.\"trial_end\", u0.\"subscription\", u0.\"settings\", u0.\"default_workspace_id\", u0.\"plan_key\", u0.\"inserted_at\", u0.\"updated_at\" FROM \"users\" AS u0 WHERE (u0.\"id\" = $1) [15944]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112237458,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.1,"query":"SELECT u0.\"id\", u0.\"user_id\", u0.\"feature_key\", u0.\"inserted_at\", u0.\"updated_at\", u0.\"user_id\" FROM \"user_features\" AS u0 WHERE (u0.\"user_id\" = $1) ORDER BY u0.\"user_id\" [15944]","source":"user_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112237580,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.0,"query":"SELECT p0.\"key\", p0.\"name\", p0.\"description\", p0.\"price\", p0.\"stripe_price_id\", p0.\"highlight\", p0.\"enabled\", p0.\"cta\", p0.\"action\", p0.\"features\", p0.\"archived\", p0.\"default\", p0.\"inserted_at\", p0.\"updated_at\", p0.\"key\" FROM \"plans\" AS p0 WHERE (p0.\"key\" = $1) [\"pro_s\"]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112237800,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT f0.\"key\", f0.\"usage_key\", f0.\"type\", f0.\"reset_period\", f0.\"limit\", f0.\"name\", f0.\"description\", f0.\"coming_soon\", f0.\"inserted_at\", f0.\"updated_at\", f0.\"key\" FROM \"features\" AS f0 WHERE (f0.\"key\" = ANY($1)) [[\"crossposting_bsky\", \"scheduled_posts_l\", \"workspaces_basic\"]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112239551,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uhvjxJdq9wAAEmD","user_id":15944,"session_id":"test_session","__sentry__":{"request":{"data":{"session_id":"test_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=test_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=test_session"},"user":{"id":15944,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT f0.\"id\", f0.\"usage_key\", f0.\"type\", f0.\"value\", f0.\"workspace_id\", f0.\"user_id\", f0.\"feature_key\", f0.\"inserted_at\", f0.\"updated_at\" FROM \"feature_usages\" AS f0 WHERE (f0.\"usage_key\" = $1) AND (f0.\"user_id\" = $2) AND (f0.\"workspace_id\" = $3) [\"scheduled_posts\", 15944, 18715]","source":"feature_usages","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112243512,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for Bluesky\", :boolean, \"Support for Bluesky\", \"crossposting_bsky\", false, \"crossposting_bsky\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112243779,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for Mastodon\", :boolean, \"Support for Mastodon\", \"crossposting_mastodon\", false, \"crossposting_mastodon\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112244020,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for LinkedIn\", :boolean, \"Support for LinkedIn\", \"crossposting_linkedin\", false, \"crossposting_linkedin\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112244202,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Support for X\", :boolean, \"Support for X (Twitter)\", \"crossposting_x\", false, \"crossposting_x\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112244494,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Mastodon\", :boolean, \"Support for Mastodon\", \"crossposting_mastodon\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112244693,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Workspace invites\", :boolean, \"Invite members to workspaces\", \"workspace_invites\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112244883,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Auto scheduler\", :boolean, \"Schedule posts in advance using a beautiful calendar UI\", \"auto_scheduler\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112245254,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Scheduled posts\", :counter, \"Schedule up to 15 posts per month\", \"scheduled_posts_s\", 15, false, \"scheduled_posts\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112245434,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Scheduled posts\", :counter, \"Schedule up to 100 posts per month\", \"scheduled_posts_m\", 100, false, \"scheduled_posts\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112245654,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Scheduled posts\", :counter, \"Schedule unlimited posts\", \"scheduled_posts_l\", -1, false, \"scheduled_posts\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112245901,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Post history\", :limit, \"Access posts from the last 30 days\", \"history_s\", 30, false, \"history\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112246150,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Post history\", :limit, \"Access posts from the last 90 days\", \"history_m\", 90, false, \"history\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112246414,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Post history\", :limit, \"Access posts from the last 12 months\", \"history_l\", 12, false, \"history\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112246606,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic image uploads\", :limit, \"Upload images up to 5MB\", \"image_size_s\", 102400, false, \"image_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112246781,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Enhanced image uploads\", :limit, \"Upload images up to 10MB\", \"image_size_m\", 10485760, false, \"image_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112246909,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Professional image uploads\", :limit, \"Upload images up to 20MB\", \"image_size_l\", 20971520, false, \"image_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112247098,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic video uploads\", :limit, \"Upload videos up to 10MB\", \"video_size_s\", 102400, false, \"video_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112247264,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Enhanced video uploads\", :limit, \"Upload videos up to 50MB\", \"video_size_m\", 52428800, false, \"video_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112247488,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Professional video uploads\", :limit, \"Upload videos up to 100MB\", \"video_size_l\", 104857600, false, \"video_size\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112247795,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Image optimization\", :boolean, \"Basic image optimization\", \"image_optimization\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112247963,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Video optimization\", :boolean, \"Video optimization for all networks\", \"video_optimization\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112248143,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic @-mentions\", :boolean, \"Basic cross-network mentions\", \"basic_mentions\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112248316,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Advanced @-mentions\", :boolean, \"Advanced cross-network mentions with suggestions\", \"advanced_mentions\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112248590,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic workspace\", :limit, \"1 workspace\", \"workspaces_basic\", 1, false, \"workspaces\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112248767,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Growth workspaces\", :limit, \"3 workspaces\", \"workspaces_growth\", 3, false, \"workspaces\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112249029,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"limit\",\"coming_soon\",\"usage_key\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Unlimited workspaces\", :limit, \"Unlimited workspaces with team support\", \"workspaces_unlimited\", -1, false, \"workspaces\", :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112249351,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Basic support\", :boolean, \"Email support with 48-hour response time\", \"customer_support\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112249558,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.1,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Priority support\", :boolean, \"Email support with 24-hour response time\", \"priority_support\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112249725,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.0,"query":"INSERT INTO \"features\" (\"name\",\"type\",\"description\",\"key\",\"coming_soon\",\"reset_period\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) ON CONFLICT (\"key\") DO UPDATE SET \"updated_at\" = EXCLUDED.\"updated_at\",\"inserted_at\" = EXCLUDED.\"inserted_at\",\"coming_soon\" = EXCLUDED.\"coming_soon\",\"description\" = EXCLUDED.\"description\",\"name\" = EXCLUDED.\"name\",\"limit\" = EXCLUDED.\"limit\",\"reset_period\" = EXCLUDED.\"reset_period\",\"type\" = EXCLUDED.\"type\",\"usage_key\" = EXCLUDED.\"usage_key\" [\"Premium support\", :boolean, \"Priority support with live chat and 12-hour response\", \"premium_support\", false, :monthly, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112250830,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"plans\" (\"default\",\"enabled\",\"name\",\"description\",\"key\",\"features\",\"highlight\",\"price\",\"stripe_price_id\",\"archived\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12) ON CONFLICT (\"key\") DO UPDATE SET \"features\" = EXCLUDED.\"features\",\"updated_at\" = EXCLUDED.\"updated_at\",\"default\" = EXCLUDED.\"default\" RETURNING \"updated_at\",\"inserted_at\",\"default\",\"archived\",\"features\",\"action\",\"cta\",\"enabled\",\"highlight\",\"stripe_price_id\",\"price\",\"description\",\"name\",\"key\" [false, true, \"Pro\", \"Pro plan\", \"pro_s\", [\"crossposting_bsky\", \"workspaces_basic\", \"scheduled_posts_l\"], false, 1000, \"price_mock123\", false, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"plans","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112250976,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.0,"query":"begin []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.6,"query":"INSERT INTO \"users\" (\"info\",\"status\",\"settings\",\"timezone\",\"customer\",\"email\",\"confirmed_at\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\" [%{}, :trial, %Crosspost.Accounts.User.Settings{onboarding_completed: false, sidebar_hidden: false, default_networks: []}, \"Etc/UTC\", false, \"<EMAIL>\", ~U[2025-07-09 19:08:32Z], ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"magenta","db":0.0,"query":"commit []"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":****************,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.4,"query":"INSERT INTO \"workspaces\" (\"name\",\"slug\",\"settings\",\"is_default\",\"owner_id\",\"invitations\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING \"id\" [\"Test Workspace -576460752303420286\", \"test-workspace--576460752303420286-hwcvsq\", %{}, true, 15945, [], ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112253091,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"yellow","db":0.2,"query":"UPDATE \"users\" SET \"default_workspace_id\" = $1, \"updated_at\" = $2 WHERE \"id\" = $3 [18716, ~N[2025-07-09 19:08:32], 15945]","source":"users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112253411,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":0.2,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18716]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112253861,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"workspace_users\" (\"user_id\",\"settings\",\"workspace_id\",\"inserted_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5) RETURNING \"id\" [15945, %{}, 18716, ~N[2025-07-09 19:08:32], ~N[2025-07-09 19:08:32]]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112255315,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","db":1.3,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15945]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112255820,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"green","db":0.2,"query":"INSERT INTO \"users_tokens\" (\"context\",\"token\",\"user_id\",\"inserted_at\") VALUES ($1,$2,$3,$4) RETURNING \"id\" [\"session\", <<77, 194, 224, 97, 200, 104, 238, 168, 239, 200, 98, 150, 186, 136, 53, 153, 237, 216, 218, 252, 67, 6, 146, 84, 210, 117, 145, 120, 89, 47, 25, 204>>, 15945, ~U[2025-07-09 19:08:32Z]]","source":"users_tokens","status":"OK"}}
{"message":"GET /payment/success","timestamp":*************,"level":"info","payload":{"function":"phoenix_endpoint_start/4","line":223,"module":"Elixir.Phoenix.Logger","time":1752088112255883,"file":"lib/phoenix/logger.ex","domain":["elixir"],"application":"phoenix","mfa":"{Phoenix.Logger, :phoenix_endpoint_start, 4}","request_id":"GFCq2uoyKt3C8-wAAA0C","__sentry__":{"request":{"data":{"session_id":"invalid_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=invalid_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=invalid_session"}}}}
{"message":"Processing with CrosspostWeb.PaymentController.success/2\n  Parameters: %{\"session_id\" => \"invalid_session\"}\n  Pipelines: [:user_browser, :require_authenticated_user, :workspace]","timestamp":*************,"level":"debug","payload":{"function":"phoenix_router_dispatch_start/4","line":284,"module":"Elixir.Phoenix.Logger","time":1752088112255948,"file":"lib/phoenix/logger.ex","domain":["elixir"],"application":"phoenix","mfa":"{Phoenix.Logger, :phoenix_router_dispatch_start, 4}","request_id":"GFCq2uoyKt3C8-wAAA0C","__sentry__":{"request":{"data":{"session_id":"invalid_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=invalid_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=invalid_session"}}}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112256695,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uoyKt3C8-wAAA0C","__sentry__":{"request":{"data":{"session_id":"invalid_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=invalid_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=invalid_session"}},"db":0.5,"query":"SELECT u1.\"id\", u1.\"uid\", u1.\"status\", u1.\"customer\", u1.\"email\", u1.\"name\", u1.\"hashed_password\", u1.\"confirmed_at\", u1.\"info\", u1.\"timezone\", u1.\"trial_end\", u1.\"subscription\", u1.\"settings\", u1.\"default_workspace_id\", u1.\"plan_key\", u1.\"inserted_at\", u1.\"updated_at\" FROM \"users_tokens\" AS u0 INNER JOIN \"users\" AS u1 ON u1.\"id\" = u0.\"user_id\" WHERE ((u0.\"token\" = $1) AND (u0.\"context\" = $2)) AND (u0.\"inserted_at\" > $3::timestamp + (-(60)::numeric * interval '1 day')) [<<77, 194, 224, 97, 200, 104, 238, 168, 239, 200, 98, 150, 186, 136, 53, 153, 237, 216, 218, 252, 67, 6, 146, 84, 210, 117, 145, 120, 89, 47, 25, 204>>, \"session\", ~U[2025-07-09 19:08:32.255979Z]]","source":"users_tokens","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112257395,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uoyKt3C8-wAAA0C","__sentry__":{"request":{"data":{"session_id":"invalid_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=invalid_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=invalid_session"}},"db":0.6,"query":"SELECT e0.\"user_id\", e0.\"feature_key\", e0.\"feature_type\", e0.\"feature_name\", e0.\"feature_description\", e0.\"feature_coming_soon\", e0.\"usage_key\", e0.\"current_usage\", e0.\"limit\", e0.\"plan_key\", e0.\"reset_period\", e0.\"period_start\", e0.\"period_end\", e0.\"order\", e0.\"user_id\" FROM \"enabled_features\" AS e0 WHERE (e0.\"user_id\" = $1) ORDER BY e0.\"user_id\" [15945]","source":"enabled_features","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112257742,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uoyKt3C8-wAAA0C","user_id":15945,"__sentry__":{"request":{"data":{"session_id":"invalid_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=invalid_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=invalid_session"},"user":{"id":15945,"username":null,"email":"<EMAIL>"}},"db":0.2,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18716]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112258123,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uoyKt3C8-wAAA0C","user_id":15945,"__sentry__":{"request":{"data":{"session_id":"invalid_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=invalid_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=invalid_session"},"user":{"id":15945,"username":null,"email":"<EMAIL>"}},"db":0.2,"query":"SELECT w0.\"id\", w0.\"workspace_id\", w0.\"user_id\", w0.\"settings\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"user_id\" FROM \"workspace_users\" AS w0 WHERE (w0.\"user_id\" = $1) ORDER BY w0.\"user_id\" [15945]","source":"workspace_users","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112258306,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uoyKt3C8-wAAA0C","user_id":15945,"__sentry__":{"request":{"data":{"session_id":"invalid_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=invalid_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=invalid_session"},"user":{"id":15945,"username":null,"email":"<EMAIL>"}},"db":0.1,"query":"SELECT w0.\"id\", w0.\"name\", w0.\"slug\", w0.\"settings\", w0.\"is_default\", w0.\"invitations\", w0.\"owner_id\", w0.\"inserted_at\", w0.\"updated_at\", w0.\"id\" FROM \"workspaces\" AS w0 WHERE (w0.\"id\" = $1) [18716]","source":"workspaces","status":"OK"}}
{"timestamp":*************,"level":"debug","payload":{"function":"log/4","line":1294,"module":"Elixir.Ecto.Adapters.SQL","time":1752088112258856,"file":"lib/ecto/adapters/sql.ex","domain":["elixir"],"application":"ecto_sql","mfa":"{Ecto.Adapters.SQL, :log, 4}","ansi_color":"cyan","request_id":"GFCq2uoyKt3C8-wAAA0C","user_id":15945,"__sentry__":{"request":{"data":{"session_id":"invalid_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=invalid_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=invalid_session"},"user":{"id":15945,"username":null,"email":"<EMAIL>"}},"db":0.5,"query":"SELECT c0.\"id\", c0.\"platform\", c0.\"platform_user_id\", c0.\"encrypted_access_token\", c0.\"encrypted_refresh_token\", c0.\"expires_at\", c0.\"settings\", c0.\"info\", c0.\"user_id\", c0.\"workspace_id\", c0.\"inserted_at\", c0.\"updated_at\", c0.\"workspace_id\" FROM \"connections\" AS c0 INNER JOIN \"workspace_users\" AS w1 ON (w1.\"workspace_id\" = c0.\"workspace_id\") AND (w1.\"user_id\" = $1) WHERE (c0.\"workspace_id\" = $2) AND (c0.\"workspace_id\" = $3) ORDER BY c0.\"workspace_id\" [15945, 18716, 18716]","source":"connections","status":"OK"}}
{"message":"Processing successful payment","timestamp":*************,"level":"info","payload":{"function":"success/2","line":12,"module":"Elixir.CrosspostWeb.PaymentController","time":1752088112258894,"file":"lib/crosspost_web/controllers/payment_controller.ex","domain":["elixir"],"application":"crosspost","mfa":"{CrosspostWeb.PaymentController, :success, 2}","request_id":"GFCq2uoyKt3C8-wAAA0C","user_id":15945,"session_id":"invalid_session","__sentry__":{"request":{"data":{"session_id":"invalid_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=invalid_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=invalid_session"},"user":{"id":15945,"username":null,"email":"<EMAIL>"}}}}
{"message":"Error processing payment","timestamp":*************,"level":"error","payload":{"error":"invalid_session","function":"success/2","line":26,"module":"Elixir.CrosspostWeb.PaymentController","time":1752088112258948,"file":"lib/crosspost_web/controllers/payment_controller.ex","domain":["elixir"],"application":"crosspost","mfa":"{CrosspostWeb.PaymentController, :success, 2}","request_id":"GFCq2uoyKt3C8-wAAA0C","user_id":15945,"session_id":"invalid_session","__sentry__":{"request":{"data":{"session_id":"invalid_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=invalid_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=invalid_session"},"user":{"id":15945,"username":null,"email":"<EMAIL>"}}}}
{"message":"Sent 302 in 10ms","timestamp":*************,"level":"info","payload":{"function":"phoenix_endpoint_stop/4","line":237,"module":"Elixir.Phoenix.Logger","time":1752088112266240,"file":"lib/phoenix/logger.ex","domain":["elixir"],"application":"phoenix","mfa":"{Phoenix.Logger, :phoenix_endpoint_stop, 4}","request_id":"GFCq2uoyKt3C8-wAAA0C","user_id":15945,"session_id":"invalid_session","__sentry__":{"request":{"data":{"session_id":"invalid_session"},"env":{"REMOTE_ADDR":"127.0.0.1","REMOTE_PORT":null,"REQUEST_ID":null,"SERVER_NAME":"www.example.com","SERVER_PORT":80},"url":"http://www.example.com/payment/success?session_id=invalid_session","cookies":{},"headers":{},"method":"GET","query_string":"session_id=invalid_session"},"user":{"id":15945,"username":null,"email":"<EMAIL>"}}}}
