defmodule CrosspostWeb.PaymentControllerTest do
  use CrosspostWeb.ConnCase

  import Crosspost.Fixtures
  import Mox

  alias Crosspost.Repo
  alias Crosspost.Accounts.FeatureUsage

  setup :verify_on_exit!

  setup do
    plan =
      plan_fixture(%{
        name: "Pro",
        key: "pro_s",
        stripe_price_id: "price_mock123",
        description: "Pro plan",
        price: 1000,
        features: ["crossposting_bsky", "workspaces_basic", "scheduled_posts_l"],
        enabled: true
      })

    %{plan: plan}
  end

  describe "GET /payment/success" do
    test "redirects to sign in when no user is authenticated", %{conn: conn} do
      conn = get(conn, ~p"/payment/success?session_id=test_session")
      assert redirected_to(conn) == ~p"/users/sign-in"

      assert Phoenix.Flash.get(conn.assigns.flash, :error) ==
               "You must log in to access this page."
    end

    test "updates user subscription when session is valid", %{conn: conn, plan: plan} do
      user = user_with_plan_fixture(%{plan: plan})

      Crosspost.Billing.Mock
      |> expect(:get_session, fn "test_session" ->
        {:ok,
         %{
           customer: "cus_mock123",
           subscription: %{
             id: "sub_mock123",
             status: "active",
             created: System.system_time(:second),
             current_period_end: System.system_time(:second) + 30 * 24 * 60 * 60,
             items: %{
               data: [
                 %{
                   price: %{
                     id: "price_mock123",
                     product: "prod_mock123"
                   }
                 }
               ]
             }
           }
         }}
      end)
      |> expect(:get_product, fn "prod_mock123" ->
        {:ok, %{id: "prod_mock123"}}
      end)
      |> expect(:get_features, fn "prod_mock123" ->
        {:ok,
         %{
           data: [
             %{entitlement_feature: %{lookup_key: "crossposting_bsky"}},
             %{entitlement_feature: %{lookup_key: "workspaces_basic"}}
           ]
         }}
      end)

      conn =
        conn
        |> log_in_user(user)
        |> get(~p"/payment/success?session_id=test_session")

      updated_user =
        Crosspost.Repo.get!(Crosspost.Accounts.User, user.id)
        |> Crosspost.Repo.preload([:plan, :features])

      assert updated_user.subscription.status == "active"
      assert updated_user.subscription.plan == plan.key
      assert updated_user.subscription.stripe_customer_id == "cus_mock123"
      assert updated_user.subscription.stripe_subscription_id == "sub_mock123"
      assert updated_user.subscription.stripe_product_id == "prod_mock123"
      assert updated_user.subscription.stripe_price_id == "price_mock123"
      assert updated_user.trial_end == nil

      assert Enum.map(updated_user.features, & &1.key) |> Enum.sort() == [
               "crossposting_bsky",
               "scheduled_posts_l",
               "workspaces_basic"
             ]

      assert html_response(conn, 200) =~ "Payment Successful"
    end

    test "upgrades user from basic to pro plan and updates feature usages", %{
      conn: conn,
      plan: pro_plan
    } do
      # Create basic plan
      basic_plan =
        plan_fixture(%{
          name: "Basic",
          key: "basic",
          stripe_price_id: "price_basic123",
          description: "Basic plan",
          price: 300,
          features: ["scheduled_posts_s", "workspaces_basic"],
          enabled: true
        })

      # Create user with basic plan and some feature usages
      user = user_fixture(%{plan_key: basic_plan.key})
      workspace = Repo.get!(Crosspost.Accounts.Workspace, user.default_workspace_id)

      # Create some feature usages for the basic plan
      initial_usage =
        feature_usage_fixture(%{
          user: user,
          workspace_id: workspace.id,
          feature_key: "scheduled_posts_s",
          usage_key: "scheduled_posts",
          type: :counter,
          value: 10
        })

      # Verify initial state
      assert initial_usage.feature_key == "scheduled_posts_s"
      assert initial_usage.value == 10

      # Verify the feature usage exists in the database
      assert Repo.get_by(FeatureUsage,
               user_id: user.id,
               workspace_id: workspace.id,
               usage_key: "scheduled_posts"
             )

      # Set up mock expectations for the billing provider
      Crosspost.Billing.Mock
      |> expect(:get_session, fn "test_session" ->
        {:ok,
         %{
           customer: "cus_mock123",
           subscription: %{
             id: "sub_mock123",
             status: "active",
             created: System.system_time(:second),
             current_period_end: System.system_time(:second) + 30 * 24 * 60 * 60,
             items: %{
               data: [
                 %{
                   price: %{
                     id: "price_mock123",
                     product: "prod_mock123"
                   }
                 }
               ]
             }
           }
         }}
      end)
      |> expect(:get_product, fn "prod_mock123" ->
        {:ok, %{id: "prod_mock123"}}
      end)
      |> expect(:get_features, fn "prod_mock123" ->
        {:ok,
         %{
           data: [
             %{entitlement_feature: %{lookup_key: "crossposting"}},
             %{entitlement_feature: %{lookup_key: "workspaces_basic"}},
             %{entitlement_feature: %{lookup_key: "scheduled_posts_l"}}
           ]
         }}
      end)

      # Simulate successful payment and plan upgrade
      conn =
        conn
        |> log_in_user(user)
        |> get(~p"/payment/success?session_id=test_session")

      # Verify the response
      assert html_response(conn, 200) =~ "Payment Successful"

      # Verify user's plan and subscription are updated
      updated_user =
        Crosspost.Repo.get!(Crosspost.Accounts.User, user.id)
        |> Crosspost.Repo.preload([:plan, :features])

      assert updated_user.plan_key == pro_plan.key
      assert updated_user.subscription.status == "active"
      assert updated_user.subscription.plan == pro_plan.key

      # Verify feature usages are updated according to the new plan
      updated_feature_usage =
        Crosspost.FeatureUsages.get_counter(updated_user, workspace, "scheduled_posts")

      assert updated_feature_usage.feature_key == "scheduled_posts_l"
      # Value should be preserved during upgrade
      assert updated_feature_usage.value == 10
    end

    test "handles error when session retrieval fails", %{conn: conn} do
      user = user_fixture()

      Crosspost.Billing.Mock
      |> expect(:get_session, fn "invalid_session" ->
        {:error, :invalid_session}
      end)

      conn =
        conn
        |> log_in_user(user)
        |> get(~p"/payment/success?session_id=invalid_session")

      assert redirected_to(conn) == ~p"/plans"
      assert Phoenix.Flash.get(conn.assigns.flash, :error) =~ "Error processing subscription"
    end
  end
end
